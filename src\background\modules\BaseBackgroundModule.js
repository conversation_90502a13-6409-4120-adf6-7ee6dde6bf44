// 后台模块基类 - 所有后台模块的基类
export class BaseBackgroundModule {
    constructor(name, dependencies = {}) {
        this.name = name;
        this.dependencies = dependencies;
        this.isEnabled = true;
        this.isInitialized = false;
        this.messageListeners = new Map();
        this.eventListeners = new Map();
        
        console.log(`📦 后台模块 ${this.name} 已创建`);
    }

    // 初始化模块
    async init() {
        if (this.isInitialized) {
            console.warn(`⚠️ 后台模块 ${this.name} 已经初始化过了`);
            return;
        }

        try {
            console.log(`🚀 初始化后台模块 ${this.name}`);
            
            // 性能监控开始
            const startTime = performance.now();
            
            // 调用子类的初始化方法
            await this.onInit();
            
            this.isInitialized = true;
            
            // 性能监控结束
            const duration = performance.now() - startTime;
            console.log(`✅ 后台模块 ${this.name} 初始化完成，耗时: ${duration.toFixed(2)}ms`);
            
        } catch (error) {
            console.error(`❌ 后台模块 ${this.name} 初始化失败:`, error);
            throw error;
        }
    }

    // 子类需要重写的初始化方法
    async onInit() {
        // 子类实现具体的初始化逻辑
    }

    // 销毁模块
    async destroy() {
        if (!this.isInitialized) {
            return;
        }

        try {
            console.log(`🗑️ 销毁后台模块 ${this.name}`);
            
            // 清理消息监听器
            this.clearAllMessageListeners();
            
            // 清理事件监听器
            this.clearAllEventListeners();
            
            // 调用子类的销毁方法
            await this.onDestroy();
            
            this.isInitialized = false;
            console.log(`✅ 后台模块 ${this.name} 已销毁`);
            
        } catch (error) {
            console.error(`❌ 后台模块 ${this.name} 销毁失败:`, error);
        }
    }

    // 子类需要重写的销毁方法
    async onDestroy() {
        // 子类实现具体的清理逻辑
    }

    // 启用模块
    enable() {
        this.isEnabled = true;
        console.log(`✅ 后台模块 ${this.name} 已启用`);
    }

    // 禁用模块
    disable() {
        this.isEnabled = false;
        console.log(`❌ 后台模块 ${this.name} 已禁用`);
    }

    // 检查模块是否可用
    isAvailable() {
        return this.isEnabled && this.isInitialized;
    }

    // 添加消息监听器
    addMessageListener(action, handler, options = {}) {
        if (!this.isAvailable()) {
            console.warn(`⚠️ 模块 ${this.name} 不可用，无法添加消息监听器`);
            return;
        }

        const listenerId = `${this.name}_${action}_${Date.now()}`;
        
        const wrappedHandler = (message, sender, sendResponse) => {
            if (message.action === action) {
                try {
                    return handler(message, sender, sendResponse);
                } catch (error) {
                    console.error(`❌ 模块 ${this.name} 处理消息 ${action} 时出错:`, error);
                    if (sendResponse) {
                        sendResponse({ error: error.message });
                    }
                }
            }
        };

        // 添加到Chrome runtime
        chrome.runtime.onMessage.addListener(wrappedHandler);
        
        // 保存引用以便清理
        this.messageListeners.set(listenerId, {
            action,
            handler: wrappedHandler,
            originalHandler: handler,
            options
        });

        console.log(`📝 模块 ${this.name} 添加消息监听器: ${action}`);
        return listenerId;
    }

    // 移除消息监听器
    removeMessageListener(listenerId) {
        const listener = this.messageListeners.get(listenerId);
        if (listener) {
            chrome.runtime.onMessage.removeListener(listener.handler);
            this.messageListeners.delete(listenerId);
            console.log(`🗑️ 模块 ${this.name} 移除消息监听器: ${listener.action}`);
            return true;
        }
        return false;
    }

    // 清理所有消息监听器
    clearAllMessageListeners() {
        for (const [listenerId, listener] of this.messageListeners) {
            chrome.runtime.onMessage.removeListener(listener.handler);
        }
        this.messageListeners.clear();
        console.log(`🧹 模块 ${this.name} 已清理所有消息监听器`);
    }

    // 添加事件监听器
    addEventListener(target, event, handler, options = {}) {
        if (!this.isAvailable()) {
            console.warn(`⚠️ 模块 ${this.name} 不可用，无法添加事件监听器`);
            return;
        }

        const listenerId = `${this.name}_${event}_${Date.now()}`;
        
        target.addEventListener(event, handler, options);
        
        // 保存引用以便清理
        this.eventListeners.set(listenerId, {
            target,
            event,
            handler,
            options
        });

        return listenerId;
    }

    // 移除事件监听器
    removeEventListener(listenerId) {
        const listener = this.eventListeners.get(listenerId);
        if (listener) {
            listener.target.removeEventListener(listener.event, listener.handler, listener.options);
            this.eventListeners.delete(listenerId);
            return true;
        }
        return false;
    }

    // 清理所有事件监听器
    clearAllEventListeners() {
        for (const [listenerId, listener] of this.eventListeners) {
            listener.target.removeEventListener(listener.event, listener.handler, listener.options);
        }
        this.eventListeners.clear();
        console.log(`🧹 模块 ${this.name} 已清理所有事件监听器`);
    }

    // 发送消息到前端
    sendMessageToUI(message, tabId = null) {
        if (tabId) {
            // 发送到特定标签页
            chrome.tabs.sendMessage(tabId, message, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn(`⚠️ 模块 ${this.name} 发送消息到标签页 ${tabId} 失败:`, chrome.runtime.lastError);
                }
            });
        } else {
            // 广播到所有标签页
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    chrome.tabs.sendMessage(tab.id, message, (response) => {
                        if (chrome.runtime.lastError) {
                            // 忽略无法发送的标签页
                        }
                    });
                });
            });
        }
    }

    // 发送状态更新
    sendStateUpdate(state, tabId = null) {
        this.sendMessageToUI({
            action: 'stateUpdate',
            state: state
        }, tabId);
    }

    // 存储数据
    async setStorage(key, value) {
        try {
            await chrome.storage.local.set({ [key]: value });
            console.log(`💾 模块 ${this.name} 保存数据: ${key}`);
        } catch (error) {
            console.error(`❌ 模块 ${this.name} 保存数据失败:`, error);
            throw error;
        }
    }

    // 获取存储数据
    async getStorage(key, defaultValue = null) {
        try {
            const result = await chrome.storage.local.get([key]);
            return result[key] !== undefined ? result[key] : defaultValue;
        } catch (error) {
            console.error(`❌ 模块 ${this.name} 获取数据失败:`, error);
            return defaultValue;
        }
    }

    // 创建定时器（自动管理）
    createTimer(callback, interval, immediate = false) {
        const timerId = setInterval(callback, interval);
        
        // 如果需要立即执行
        if (immediate) {
            callback();
        }
        
        // 保存定时器ID以便清理
        if (!this.timers) {
            this.timers = new Set();
        }
        this.timers.add(timerId);
        
        return timerId;
    }

    // 清理定时器
    clearTimer(timerId) {
        if (this.timers && this.timers.has(timerId)) {
            clearInterval(timerId);
            this.timers.delete(timerId);
            return true;
        }
        return false;
    }

    // 清理所有定时器
    clearAllTimers() {
        if (this.timers) {
            this.timers.forEach(timerId => {
                clearInterval(timerId);
            });
            this.timers.clear();
            console.log(`🧹 模块 ${this.name} 已清理所有定时器`);
        }
    }

    // 获取模块状态
    getStatus() {
        return {
            name: this.name,
            isEnabled: this.isEnabled,
            isInitialized: this.isInitialized,
            isAvailable: this.isAvailable(),
            messageListeners: this.messageListeners.size,
            eventListeners: this.eventListeners.size,
            timers: this.timers ? this.timers.size : 0
        };
    }

    // 日志方法
    log(...args) {
        console.log(`[${this.name}]`, ...args);
    }

    warn(...args) {
        console.warn(`[${this.name}]`, ...args);
    }

    error(...args) {
        console.error(`[${this.name}]`, ...args);
    }
}

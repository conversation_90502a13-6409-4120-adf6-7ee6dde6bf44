// 神灯AI·灵阅 - 模块化系统切换脚本
// 在浏览器控制台中运行此脚本来启用新的模块化系统

console.log("🚀 神灯AI·灵阅模块化系统切换脚本");

// 检查当前环境
function checkEnvironment() {
    console.log("🔍 检查当前环境...");
    
    // 检查是否在扩展环境中
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.error("❌ 请在Chrome扩展环境中运行此脚本");
        return false;
    }
    
    // 检查configManager是否可用
    if (typeof configManager === 'undefined') {
        console.error("❌ configManager不可用，请确保在正确的页面中运行");
        return false;
    }
    
    console.log("✅ 环境检查通过");
    return true;
}

// 显示当前状态
function showCurrentStatus() {
    console.log("📊 当前模块系统状态:");
    
    try {
        const frontendEnabled = configManager.getConfig('debug.useNewModules', false);
        const backendEnabled = configManager.getConfig('debug.useNewBackgroundModules', false);
        
        console.log(`  前端模块系统: ${frontendEnabled ? '✅ 已启用' : '❌ 未启用'}`);
        console.log(`  后台模块系统: ${backendEnabled ? '✅ 已启用' : '❌ 未启用'}`);
        
        return { frontendEnabled, backendEnabled };
    } catch (error) {
        console.error("❌ 获取状态失败:", error);
        return null;
    }
}

// 启用前端模块系统
function enableFrontendModules() {
    try {
        configManager.setConfig('debug.useNewModules', true);
        console.log("✅ 前端模块系统已启用");
        return true;
    } catch (error) {
        console.error("❌ 启用前端模块系统失败:", error);
        return false;
    }
}

// 启用后台模块系统
function enableBackendModules() {
    try {
        configManager.setConfig('debug.useNewBackgroundModules', true);
        console.log("✅ 后台模块系统已启用");
        return true;
    } catch (error) {
        console.error("❌ 启用后台模块系统失败:", error);
        return false;
    }
}

// 禁用前端模块系统
function disableFrontendModules() {
    try {
        configManager.setConfig('debug.useNewModules', false);
        console.log("❌ 前端模块系统已禁用");
        return true;
    } catch (error) {
        console.error("❌ 禁用前端模块系统失败:", error);
        return false;
    }
}

// 禁用后台模块系统
function disableBackendModules() {
    try {
        configManager.setConfig('debug.useNewBackgroundModules', false);
        console.log("❌ 后台模块系统已禁用");
        return true;
    } catch (error) {
        console.error("❌ 禁用后台模块系统失败:", error);
        return false;
    }
}

// 完全启用模块化系统
function enableModularSystem() {
    console.log("🔄 启用完整的模块化系统...");
    
    const frontendSuccess = enableFrontendModules();
    const backendSuccess = enableBackendModules();
    
    if (frontendSuccess && backendSuccess) {
        console.log("🎉 模块化系统已完全启用！");
        console.log("📝 请刷新侧边栏页面以应用前端模块系统");
        console.log("📝 后台模块系统将在下次扩展重启时生效");
        return true;
    } else {
        console.error("❌ 模块化系统启用失败");
        return false;
    }
}

// 完全禁用模块化系统
function disableModularSystem() {
    console.log("🔄 禁用完整的模块化系统...");
    
    const frontendSuccess = disableFrontendModules();
    const backendSuccess = disableBackendModules();
    
    if (frontendSuccess && backendSuccess) {
        console.log("✅ 模块化系统已完全禁用");
        console.log("📝 请刷新侧边栏页面以恢复原始系统");
        console.log("📝 后台将在下次扩展重启时恢复原始系统");
        return true;
    } else {
        console.error("❌ 模块化系统禁用失败");
        return false;
    }
}

// 重新加载扩展
function reloadExtension() {
    console.log("🔄 重新加载扩展...");
    
    try {
        chrome.runtime.reload();
        console.log("✅ 扩展重新加载命令已发送");
    } catch (error) {
        console.error("❌ 重新加载扩展失败:", error);
        console.log("💡 请手动在扩展管理页面重新加载扩展");
    }
}

// 刷新当前页面
function refreshCurrentPage() {
    console.log("🔄 刷新当前页面...");
    
    try {
        location.reload();
        console.log("✅ 页面刷新命令已发送");
    } catch (error) {
        console.error("❌ 刷新页面失败:", error);
        console.log("💡 请手动刷新页面");
    }
}

// 显示帮助信息
function showHelp() {
    console.log("📖 神灯AI·灵阅模块化系统切换帮助");
    console.log("");
    console.log("🔧 可用命令:");
    console.log("  showCurrentStatus() - 显示当前模块系统状态");
    console.log("  enableModularSystem() - 启用完整的模块化系统");
    console.log("  disableModularSystem() - 禁用完整的模块化系统");
    console.log("  enableFrontendModules() - 仅启用前端模块系统");
    console.log("  enableBackendModules() - 仅启用后台模块系统");
    console.log("  disableFrontendModules() - 仅禁用前端模块系统");
    console.log("  disableBackendModules() - 仅禁用后台模块系统");
    console.log("  reloadExtension() - 重新加载扩展");
    console.log("  refreshCurrentPage() - 刷新当前页面");
    console.log("  showHelp() - 显示此帮助信息");
    console.log("");
    console.log("💡 推荐使用流程:");
    console.log("  1. showCurrentStatus() - 查看当前状态");
    console.log("  2. enableModularSystem() - 启用模块化系统");
    console.log("  3. refreshCurrentPage() - 刷新页面应用前端模块");
    console.log("  4. reloadExtension() - 重启扩展应用后台模块");
    console.log("");
    console.log("🔄 回滚流程:");
    console.log("  1. disableModularSystem() - 禁用模块化系统");
    console.log("  2. refreshCurrentPage() - 刷新页面");
    console.log("  3. reloadExtension() - 重启扩展");
}

// 主函数
function main() {
    console.log("=" .repeat(60));
    console.log("🚀 神灯AI·灵阅模块化系统切换脚本");
    console.log("=" .repeat(60));
    
    // 检查环境
    if (!checkEnvironment()) {
        return;
    }
    
    // 显示当前状态
    const status = showCurrentStatus();
    if (!status) {
        return;
    }
    
    console.log("");
    console.log("🎯 快速操作:");
    
    if (!status.frontendEnabled && !status.backendEnabled) {
        console.log("💡 模块化系统未启用，运行 enableModularSystem() 来启用");
    } else if (status.frontendEnabled && status.backendEnabled) {
        console.log("✅ 模块化系统已完全启用");
        console.log("💡 运行 disableModularSystem() 来禁用");
    } else {
        console.log("⚠️ 模块化系统部分启用，运行 enableModularSystem() 来完全启用");
    }
    
    console.log("");
    console.log("📖 运行 showHelp() 查看所有可用命令");
    console.log("=" .repeat(60));
}

// 将函数添加到全局作用域
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.showCurrentStatus = showCurrentStatus;
    window.enableModularSystem = enableModularSystem;
    window.disableModularSystem = disableModularSystem;
    window.enableFrontendModules = enableFrontendModules;
    window.enableBackendModules = enableBackendModules;
    window.disableFrontendModules = disableFrontendModules;
    window.disableBackendModules = disableBackendModules;
    window.reloadExtension = reloadExtension;
    window.refreshCurrentPage = refreshCurrentPage;
    window.showHelp = showHelp;
} else if (typeof self !== 'undefined') {
    // Service Worker环境
    self.showCurrentStatus = showCurrentStatus;
    self.enableModularSystem = enableModularSystem;
    self.disableModularSystem = disableModularSystem;
    self.enableFrontendModules = enableFrontendModules;
    self.enableBackendModules = enableBackendModules;
    self.disableFrontendModules = disableFrontendModules;
    self.disableBackendModules = disableBackendModules;
    self.reloadExtension = reloadExtension;
    self.refreshCurrentPage = refreshCurrentPage;
    self.showHelp = showHelp;
}

// 自动运行主函数
main();

// 导出函数（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showCurrentStatus,
        enableModularSystem,
        disableModularSystem,
        enableFrontendModules,
        enableBackendModules,
        disableFrontendModules,
        disableBackendModules,
        reloadExtension,
        refreshCurrentPage,
        showHelp
    };
}

// 神灯AI·灵阅 - 模块化侧边栏主入口文件
// 这是新的模块化版本，将逐步替换原有的index.js

console.log("🚀 神灯AI·灵阅 v0.52 - 模块化侧边栏启动");

// 导入模块系统
import { moduleContainer, registerAllModules } from './modules/ModuleContainer.js';
import { configManager } from '../../utils/configManager.js';

// 全局状态
let isInitialized = false;
let initializationPromise = null;

// 主初始化函数
async function initializeSidePanel() {
    if (isInitialized) {
        console.log('侧边栏已经初始化过了');
        return;
    }
    
    if (initializationPromise) {
        console.log('侧边栏正在初始化中，等待完成...');
        return initializationPromise;
    }
    
    console.log('🔄 开始初始化模块化侧边栏...');
    
    initializationPromise = performInitialization();
    
    try {
        await initializationPromise;
        isInitialized = true;
        console.log('✅ 模块化侧边栏初始化完成');
    } catch (error) {
        console.error('❌ 模块化侧边栏初始化失败:', error);
        isInitialized = false;
        initializationPromise = null;
        throw error;
    }
}

// 执行初始化
async function performInitialization() {
    try {
        // 1. 注册所有模块
        console.log('📦 注册模块...');
        const registrationSuccess = await registerAllModules(moduleContainer);
        if (!registrationSuccess) {
            throw new Error('模块注册失败');
        }
        
        // 2. 初始化所有模块
        console.log('🚀 初始化模块...');
        const initResults = await moduleContainer.initializeAll();
        
        // 3. 检查初始化结果
        const failedModules = initResults.filter(result => !result.success);
        if (failedModules.length > 0) {
            console.warn('⚠️ 部分模块初始化失败:', failedModules);
            // 不抛出错误，允许部分功能正常工作
        }
        
        // 4. 设置模块间通信
        setupModuleCommunication();
        
        // 5. 启动核心服务
        startCoreServices();
        
        // 6. 设置调试工具
        setupDebugTools();
        
        console.log('🎉 所有模块初始化完成');
        
    } catch (error) {
        console.error('❌ 初始化过程中发生错误:', error);
        throw error;
    }
}

// 设置模块间通信
function setupModuleCommunication() {
    const eventManager = moduleContainer.getModule('EventManager');
    if (!eventManager) {
        console.warn('⚠️ EventManager未找到，跳过模块间通信设置');
        return;
    }
    
    // 设置状态更新广播
    eventManager.onStateUpdate((state) => {
        // 广播状态更新到所有需要的模块
        broadcastStateUpdate(state);
    });
    
    // 设置消息传递
    eventManager.onMessage('showMessage', (data) => {
        showGlobalMessage(data.message, data.isError);
    });
    
    console.log('📡 模块间通信已设置');
}

// 广播状态更新
function broadcastStateUpdate(state) {
    const modules = ['PlaybackController', 'VoiceManager', 'URLHandler', 'SettingsManager'];
    
    modules.forEach(moduleName => {
        const module = moduleContainer.getModule(moduleName);
        if (module && typeof module.updateState === 'function') {
            try {
                module.updateState(state);
            } catch (error) {
                console.error(`❌ 模块 ${moduleName} 状态更新失败:`, error);
            }
        }
    });
}

// 显示全局消息
function showGlobalMessage(message, isError = false) {
    const eventManager = moduleContainer.getModule('EventManager');
    if (eventManager && typeof eventManager.showMessage === 'function') {
        eventManager.showMessage(message, isError);
    } else {
        console.log(isError ? '❌' : 'ℹ️', message);
    }
}

// 启动核心服务
function startCoreServices() {
    // 启动事件管理器的核心服务
    const eventManager = moduleContainer.getModule('EventManager');
    if (eventManager) {
        // 事件管理器会自动启动状态同步和连接检查
        console.log('📡 核心服务已启动');
    }
    
    // 启动播放控制器的计时器服务
    const playbackController = moduleContainer.getModule('PlaybackController');
    if (playbackController) {
        // 播放控制器会自动启动状态同步
        console.log('⏯️ 播放控制服务已启动');
    }
}

// 设置调试工具
function setupDebugTools() {
    // 全局调试函数
    window.getModuleContainer = () => moduleContainer;
    
    window.getModule = (name) => moduleContainer.getModule(name);
    
    window.getModuleStatus = () => {
        console.log('📊 模块系统状态:', moduleContainer.getStatus());
        return moduleContainer.getStatus();
    };
    
    window.restartModule = async (name) => {
        const module = moduleContainer.getModule(name);
        if (module) {
            try {
                await module.destroy();
                await module.init();
                console.log(`✅ 模块 ${name} 已重启`);
            } catch (error) {
                console.error(`❌ 模块 ${name} 重启失败:`, error);
            }
        } else {
            console.error(`❌ 模块 ${name} 未找到`);
        }
    };
    
    window.toggleModule = (name, enabled) => {
        const module = moduleContainer.getModule(name);
        if (module) {
            if (enabled) {
                module.enable();
            } else {
                module.disable();
            }
            console.log(`${enabled ? '✅' : '❌'} 模块 ${name} 已${enabled ? '启用' : '禁用'}`);
        }
    };
    
    // 性能监控
    window.getPerformanceReport = () => {
        const performanceMonitor = moduleContainer.dependencies?.performanceMonitor;
        if (performanceMonitor && typeof performanceMonitor.getPerformanceReport === 'function') {
            return performanceMonitor.getPerformanceReport();
        }
        return null;
    };
    
    console.log('🛠️ 调试工具已设置');
    console.log('💡 可用命令:');
    console.log('  getModuleContainer() - 获取模块容器');
    console.log('  getModule(name) - 获取指定模块');
    console.log('  getModuleStatus() - 查看模块状态');
    console.log('  restartModule(name) - 重启指定模块');
    console.log('  toggleModule(name, enabled) - 启用/禁用模块');
    console.log('  getPerformanceReport() - 获取性能报告');
}

// 错误处理
function handleInitializationError(error) {
    console.error('❌ 侧边栏初始化失败:', error);
    
    // 显示错误信息给用户
    const errorMessage = document.createElement('div');
    errorMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #f44336;
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;
    errorMessage.innerHTML = `
        <h3>初始化失败</h3>
        <p>侧边栏模块化系统初始化失败</p>
        <p>错误: ${error.message}</p>
        <button onclick="location.reload()" style="
            background: white;
            color: #f44336;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        ">重新加载</button>
    `;
    
    document.body.appendChild(errorMessage);
}

// 检查是否应该使用新模块系统
function shouldUseNewModules() {
    try {
        return configManager.getConfig('debug.useNewModules', true); // 默认启用
    } catch (error) {
        console.error('检查模块系统配置失败:', error);
        return true; // 出错时也默认启用新系统
    }
}

// 主启动逻辑
async function main() {
    try {
        // 检查是否启用新模块系统
        if (!shouldUseNewModules()) {
            console.log('📝 新模块系统未启用，回退到旧系统');
            // 动态加载旧系统
            const script = document.createElement('script');
            script.src = 'index.js';
            document.head.appendChild(script);
            return;
        }

        console.log('✅ 新模块系统已启用');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }
        
        // 初始化侧边栏
        await initializeSidePanel();
        
    } catch (error) {
        handleInitializationError(error);
    }
}

// 启动应用
main();

// 导出主要函数供外部使用
export {
    initializeSidePanel,
    moduleContainer,
    shouldUseNewModules
};

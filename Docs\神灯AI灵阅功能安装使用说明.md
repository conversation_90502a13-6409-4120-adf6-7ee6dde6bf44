# 神灯AI·灵阅 V0.50 安装与使用说明

## 1. 软件概述
神灯AI·灵阅是一款基于Chrome/Edge浏览器的智能阅读增强插件，提供网页内容解析、文本转语音朗读、连续阅读等功能。支持多种语音引擎，可自定义阅读体验。

## 2. 系统要求
- Chrome 88+ 或 Microsoft Edge 88+ 浏览器
- 支持Web Speech API的现代操作系统
- 网络连接（部分功能需要）

## 3. 安装方法

### 3.1 开发者模式安装（推荐）
1. 下载插件源代码
2. 解压到本地目录
3. 打开浏览器，进入扩展程序管理页面：
   - Chrome: 在地址栏输入 `chrome://extensions/`
   - Edge: 在地址栏输入 `edge://extensions/`
4. 启用"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择解压后的插件目录

### 3.2 从商店安装（待发布）
1. 打开Chrome网上应用商店
2. 搜索"神灯AI·灵阅"
3. 点击"添加至Chrome"

## 4. 功能使用

### 4.1 基本操作
1. 点击浏览器工具栏中的插件图标打开侧边栏
2. 在任意网页上，点击"播放"按钮开始朗读
3. 使用控制面板控制播放/暂停/停止
4. 调整语速：0.5x - 10.0x 可调

### 4.2 核心功能

#### 4.2.1 智能阅读
- 自动解析网页正文内容
- 支持连续阅读多页内容
- 自动保存阅读进度
- 支持从上次中断处继续

#### 4.2.2 语音控制
- 支持多种语音选择
- 可调节语速、音调
- 支持语音收藏管理
- 自动检测页面语言

#### 4.2.3 阅读历史
- 自动记录阅读历史
- 支持从历史记录快速继续
- 可清除历史记录

### 4.3 高级功能

#### 4.3.1 URL播放
1. 在侧边栏输入框输入网页URL
2. 点击"开始朗读"按钮
3. 插件将自动加载并朗读指定页面内容

#### 4.3.2 连续阅读
1. 启用"连续阅读"开关
2. 设置连续阅读章节数（1-30章）
3. 插件将自动加载并朗读后续章节

#### 4.3.3 快捷键
- 播放/暂停: `Alt+Shift+S`
- 上一章: `Alt+←`
- 下一章: `Alt+→`
- 增加语速: `Alt+↑`
- 降低语速: `Alt+↓`

## 5. 常见问题

### 5.1 为什么某些网页无法朗读？
- 网页可能有反爬虫机制
- 页面结构复杂，无法正确解析
- 尝试刷新页面后重试

### 5.2 如何调整语音设置？
1. 点击侧边栏右上角的设置图标
2. 在语音设置面板中选择喜欢的语音
3. 调整语速、音调等参数
4. 点击"保存设置"

### 5.3 如何清除阅读历史？
1. 打开侧边栏
2. 进入"历史记录"标签
3. 点击"清空历史记录"按钮

## 6. 故障排除

### 6.1 插件无响应
1. 刷新当前页面
2. 重新加载插件
3. 重启浏览器

### 6.2 语音无法播放
1. 检查系统声音设置
2. 确认浏览器有麦克风权限
3. 尝试切换到其他语音引擎

## 7. 隐私说明
- 所有内容解析和语音合成均在本地完成
- 不会收集或上传您的浏览数据
- 阅读历史仅保存在本地浏览器中

## 8. 更新日志
### V0.50
- 新增智能内容解析引擎
- 优化语音合成效果
- 改进连续阅读体验
- 修复若干已知问题

## 9. 技术支持
如遇问题，请提供以下信息：
1. 浏览器版本
2. 操作系统版本
3. 问题描述
4. 复现步骤

联系邮箱：<EMAIL>

---

*神灯AI·灵阅 - 让阅读更智能*

// TTS管理模块 - 处理语音合成相关功能
import { BaseBackgroundModule } from './BaseBackgroundModule.js';

export class TTSManager extends BaseBackgroundModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // TTS状态
        this.isTtsAvailable = false;
        this.currentUtterance = null;
        this.currentVoice = null;
        this.currentSpeed = 1.1;
        this.currentPitch = 1.0;
        this.currentVolume = 1.0;
        
        // TTS事件处理
        this.ttsEventHandlers = {
            onstart: null,
            onend: null,
            onerror: null,
            onpause: null,
            onresume: null,
            onboundary: null
        };
        
        // 语音列表
        this.availableVoices = [];
        this.voicesLoaded = false;
        
        // 重试机制
        this.maxRetries = 3;
        this.retryDelay = 1000;
    }

    async onInit() {
        // 初始化TTS引擎
        await this.initTTS();
        
        // 设置消息监听器
        this.setupMessageListeners();
        
        // 设置TTS事件监听器
        this.setupTTSEventListeners();
        
        // 加载语音列表
        this.loadVoices();
    }

    // 初始化TTS引擎
    async initTTS() {
        try {
            this.log("初始化TTS引擎...");
            
            // 检查TTS API是否可用
            if (!chrome.tts) {
                throw new Error('TTS API不可用');
            }
            
            // 测试TTS功能
            await this.testTTS();
            
            this.isTtsAvailable = true;
            this.log("✅ TTS引擎初始化成功");
            
        } catch (error) {
            this.error("❌ TTS引擎初始化失败:", error);
            this.isTtsAvailable = false;
            throw error;
        }
    }

    // 测试TTS功能
    async testTTS() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('TTS测试超时'));
            }, 5000);
            
            chrome.tts.speak('', {
                onEvent: (event) => {
                    clearTimeout(timeout);
                    if (event.type === 'start' || event.type === 'end' || event.type === 'error') {
                        resolve();
                    }
                }
            });
        });
    }

    // 设置消息监听器
    setupMessageListeners() {
        // 开始朗读
        this.addMessageListener('startReading', (message, sender, sendResponse) => {
            this.handleStartReading(message, sendResponse);
        });

        // 暂停朗读
        this.addMessageListener('pauseReading', (message, sender, sendResponse) => {
            this.handlePauseReading(message, sendResponse);
        });

        // 恢复朗读
        this.addMessageListener('resumeReading', (message, sender, sendResponse) => {
            this.handleResumeReading(message, sendResponse);
        });

        // 停止朗读
        this.addMessageListener('stopReading', (message, sender, sendResponse) => {
            this.handleStopReading(message, sendResponse);
        });

        // 设置语音
        this.addMessageListener('setVoice', (message, sender, sendResponse) => {
            this.handleSetVoice(message, sendResponse);
        });

        // 设置速度
        this.addMessageListener('setSpeed', (message, sender, sendResponse) => {
            this.handleSetSpeed(message, sendResponse);
        });

        // 获取可用语音
        this.addMessageListener('getVoices', (message, sender, sendResponse) => {
            this.handleGetVoices(message, sendResponse);
        });

        // TTS状态检查
        this.addMessageListener('checkTTSStatus', (message, sender, sendResponse) => {
            this.handleCheckTTSStatus(message, sendResponse);
        });
    }

    // 设置TTS事件监听器
    setupTTSEventListeners() {
        // 这些事件处理器会在speak时动态设置
        this.ttsEventHandlers = {
            onstart: (event) => {
                this.log("TTS开始播放");
                this.notifyTTSEvent('start', event);
            },
            
            onend: (event) => {
                this.log("TTS播放结束");
                this.currentUtterance = null;
                this.notifyTTSEvent('end', event);
            },
            
            onerror: (event) => {
                this.error("TTS播放错误:", event);
                this.currentUtterance = null;
                this.notifyTTSEvent('error', event);
            },
            
            onpause: (event) => {
                this.log("TTS暂停");
                this.notifyTTSEvent('pause', event);
            },
            
            onresume: (event) => {
                this.log("TTS恢复");
                this.notifyTTSEvent('resume', event);
            },
            
            onboundary: (event) => {
                // 单词边界事件，可用于进度跟踪
                this.notifyTTSEvent('boundary', event);
            }
        };
    }

    // 加载语音列表
    loadVoices() {
        chrome.tts.getVoices((voices) => {
            this.availableVoices = voices || [];
            this.voicesLoaded = true;
            this.log(`已加载 ${this.availableVoices.length} 个语音`);
            
            // 如果没有设置默认语音，选择第一个中文语音
            if (!this.currentVoice && this.availableVoices.length > 0) {
                const chineseVoice = this.availableVoices.find(voice => 
                    voice.lang && voice.lang.startsWith('zh')
                );
                this.currentVoice = chineseVoice || this.availableVoices[0];
                this.log("设置默认语音:", this.currentVoice.name);
            }
        });
    }

    // 处理开始朗读
    async handleStartReading(message, sendResponse) {
        try {
            if (!this.isTtsAvailable) {
                throw new Error('TTS不可用');
            }

            const { text, options = {} } = message;
            
            if (!text || text.trim() === '') {
                throw new Error('朗读文本为空');
            }

            // 停止当前播放
            this.stopSpeaking();

            // 准备TTS选项
            const ttsOptions = {
                voiceName: this.currentVoice?.name,
                rate: this.currentSpeed,
                pitch: this.currentPitch,
                volume: this.currentVolume,
                ...options,
                ...this.ttsEventHandlers
            };

            // 开始朗读
            this.currentUtterance = text;
            chrome.tts.speak(text, ttsOptions);

            this.log("开始朗读，文本长度:", text.length);
            sendResponse({ success: true, message: '开始朗读' });

        } catch (error) {
            this.error("开始朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理暂停朗读
    handlePauseReading(message, sendResponse) {
        try {
            if (!this.isTtsAvailable) {
                throw new Error('TTS不可用');
            }

            chrome.tts.pause();
            this.log("朗读已暂停");
            sendResponse({ success: true, message: '朗读已暂停' });

        } catch (error) {
            this.error("暂停朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理恢复朗读
    handleResumeReading(message, sendResponse) {
        try {
            if (!this.isTtsAvailable) {
                throw new Error('TTS不可用');
            }

            chrome.tts.resume();
            this.log("朗读已恢复");
            sendResponse({ success: true, message: '朗读已恢复' });

        } catch (error) {
            this.error("恢复朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理停止朗读
    handleStopReading(message, sendResponse) {
        try {
            this.stopSpeaking();
            this.log("朗读已停止");
            sendResponse({ success: true, message: '朗读已停止' });

        } catch (error) {
            this.error("停止朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理设置语音
    handleSetVoice(message, sendResponse) {
        try {
            const { voice } = message;
            
            if (!voice || !voice.name) {
                throw new Error('语音参数无效');
            }

            // 验证语音是否存在
            const foundVoice = this.availableVoices.find(v => v.name === voice.name);
            if (!foundVoice) {
                throw new Error('指定的语音不存在');
            }

            this.currentVoice = foundVoice;
            this.log("语音已设置为:", this.currentVoice.name);
            
            // 保存到存储
            this.setStorage('currentVoice', this.currentVoice);
            
            sendResponse({ success: true, voice: this.currentVoice });

        } catch (error) {
            this.error("设置语音失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理设置速度
    handleSetSpeed(message, sendResponse) {
        try {
            const { speed } = message;
            
            if (typeof speed !== 'number' || speed < 0.1 || speed > 3.0) {
                throw new Error('速度参数无效，应在0.1-3.0之间');
            }

            this.currentSpeed = speed;
            this.log("朗读速度已设置为:", this.currentSpeed);
            
            // 保存到存储
            this.setStorage('currentSpeed', this.currentSpeed);
            
            sendResponse({ success: true, speed: this.currentSpeed });

        } catch (error) {
            this.error("设置速度失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理获取语音列表
    handleGetVoices(message, sendResponse) {
        try {
            if (!this.voicesLoaded) {
                this.loadVoices();
            }

            sendResponse({ 
                success: true, 
                voices: this.availableVoices,
                currentVoice: this.currentVoice
            });

        } catch (error) {
            this.error("获取语音列表失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理TTS状态检查
    handleCheckTTSStatus(message, sendResponse) {
        chrome.tts.isSpeaking((speaking) => {
            sendResponse({
                success: true,
                status: {
                    available: this.isTtsAvailable,
                    speaking: speaking,
                    currentVoice: this.currentVoice,
                    currentSpeed: this.currentSpeed,
                    voicesCount: this.availableVoices.length
                }
            });
        });
    }

    // 停止朗读
    stopSpeaking() {
        if (this.isTtsAvailable) {
            // 多次调用确保停止
            for (let i = 0; i < 3; i++) {
                chrome.tts.stop();
            }
            this.currentUtterance = null;
        }
    }

    // 检查是否正在朗读
    isSpeaking() {
        return new Promise((resolve) => {
            if (!this.isTtsAvailable) {
                resolve(false);
                return;
            }
            
            chrome.tts.isSpeaking(resolve);
        });
    }

    // 通知TTS事件
    notifyTTSEvent(eventType, eventData) {
        // 通知其他模块TTS事件
        if (this.dependencies.moduleContainer) {
            this.dependencies.moduleContainer.emit('ttsEvent', {
                type: eventType,
                data: eventData,
                timestamp: Date.now()
            });
        }

        // 发送到前端
        this.sendMessageToUI({
            action: 'ttsEvent',
            eventType: eventType,
            eventData: eventData
        });
    }

    // 获取TTS状态
    getStatus() {
        return {
            ...super.getStatus(),
            isTtsAvailable: this.isTtsAvailable,
            currentVoice: this.currentVoice?.name,
            currentSpeed: this.currentSpeed,
            voicesCount: this.availableVoices.length,
            isPlaying: !!this.currentUtterance
        };
    }

    // 公共API
    async speak(text, options = {}) {
        return new Promise((resolve, reject) => {
            this.handleStartReading({ text, options }, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    async pause() {
        return new Promise((resolve, reject) => {
            this.handlePauseReading({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    async resume() {
        return new Promise((resolve, reject) => {
            this.handleResumeReading({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    async stop() {
        return new Promise((resolve, reject) => {
            this.handleStopReading({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    setVoice(voice) {
        return new Promise((resolve, reject) => {
            this.handleSetVoice({ voice }, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    setSpeed(speed) {
        return new Promise((resolve, reject) => {
            this.handleSetSpeed({ speed }, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    getVoices() {
        return new Promise((resolve, reject) => {
            this.handleGetVoices({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }
}

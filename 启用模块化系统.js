// 神灯AI·灵阅 - 安全启用模块化系统脚本
// 在浏览器控制台中运行此脚本来安全启用新的模块化系统

console.log("🚀 神灯AI·灵阅模块化系统安全启用脚本");

// 检查当前环境
function checkEnvironment() {
    console.log("🔍 检查当前环境...");
    
    // 检查是否在扩展环境中
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.error("❌ 请在Chrome扩展环境中运行此脚本");
        return false;
    }
    
    // 检查configManager是否可用
    if (typeof configManager === 'undefined') {
        console.error("❌ configManager不可用，请确保在侧边栏页面中运行");
        return false;
    }
    
    console.log("✅ 环境检查通过");
    return true;
}

// 显示当前状态
function showCurrentStatus() {
    console.log("📊 当前模块系统状态:");
    
    try {
        const frontendEnabled = configManager.getConfig('debug.useNewModules', false);
        const backendEnabled = configManager.getConfig('debug.useNewBackgroundModules', false);
        
        console.log(`  前端模块系统: ${frontendEnabled ? '✅ 已启用' : '❌ 未启用'}`);
        console.log(`  后台模块系统: ${backendEnabled ? '✅ 已启用' : '❌ 未启用'}`);
        
        return { frontendEnabled, backendEnabled };
    } catch (error) {
        console.error("❌ 获取状态失败:", error);
        return null;
    }
}

// 检查模块文件是否存在
async function checkModuleFiles() {
    console.log("🔍 检查模块文件...");
    
    const requiredFiles = [
        'modules/BaseModule.js',
        'modules/ModuleContainer.js',
        'modules/VoiceManager.js',
        'modules/PlaybackController.js',
        'modules/URLHandler.js',
        'modules/SettingsManager.js',
        'modules/EventManager.js',
        'index_new.js'
    ];
    
    let allFilesExist = true;
    
    for (const file of requiredFiles) {
        try {
            const response = await fetch(file);
            if (!response.ok) {
                console.error(`❌ 文件不存在: ${file}`);
                allFilesExist = false;
            } else {
                console.log(`✅ 文件存在: ${file}`);
            }
        } catch (error) {
            console.error(`❌ 检查文件失败: ${file}`, error);
            allFilesExist = false;
        }
    }
    
    return allFilesExist;
}

// 测试模块导入
async function testModuleImport() {
    console.log("🧪 测试模块导入...");
    
    try {
        // 测试导入ModuleContainer
        const moduleContainerModule = await import('./modules/ModuleContainer.js');
        console.log("✅ ModuleContainer导入成功");
        
        // 测试导入BaseModule
        const baseModuleModule = await import('./modules/BaseModule.js');
        console.log("✅ BaseModule导入成功");
        
        return true;
    } catch (error) {
        console.error("❌ 模块导入测试失败:", error);
        return false;
    }
}

// 安全启用前端模块系统
async function safeEnableFrontendModules() {
    console.log("🔄 安全启用前端模块系统...");
    
    try {
        // 1. 检查文件
        const filesExist = await checkModuleFiles();
        if (!filesExist) {
            throw new Error("模块文件不完整");
        }
        
        // 2. 测试导入
        const importSuccess = await testModuleImport();
        if (!importSuccess) {
            throw new Error("模块导入测试失败");
        }
        
        // 3. 启用配置
        configManager.setConfig('debug.useNewModules', true);
        console.log("✅ 前端模块系统配置已启用");
        
        // 4. 提示刷新
        console.log("📝 请刷新页面以应用前端模块系统");
        
        return true;
    } catch (error) {
        console.error("❌ 启用前端模块系统失败:", error);
        return false;
    }
}

// 安全启用后台模块系统
function safeEnableBackendModules() {
    console.log("🔄 安全启用后台模块系统...");
    
    try {
        configManager.setConfig('debug.useNewBackgroundModules', true);
        console.log("✅ 后台模块系统配置已启用");
        console.log("📝 请重新加载扩展以应用后台模块系统");
        return true;
    } catch (error) {
        console.error("❌ 启用后台模块系统失败:", error);
        return false;
    }
}

// 完全启用模块化系统
async function safeEnableModularSystem() {
    console.log("🔄 安全启用完整的模块化系统...");
    
    const frontendSuccess = await safeEnableFrontendModules();
    const backendSuccess = safeEnableBackendModules();
    
    if (frontendSuccess && backendSuccess) {
        console.log("🎉 模块化系统已安全启用！");
        console.log("📝 下一步操作:");
        console.log("  1. 刷新当前页面: location.reload()");
        console.log("  2. 重新加载扩展: chrome.runtime.reload()");
        return true;
    } else {
        console.error("❌ 模块化系统启用失败");
        return false;
    }
}

// 禁用模块化系统
function disableModularSystem() {
    console.log("🔄 禁用模块化系统...");
    
    try {
        configManager.setConfig('debug.useNewModules', false);
        configManager.setConfig('debug.useNewBackgroundModules', false);
        console.log("✅ 模块化系统已禁用");
        console.log("📝 请刷新页面和重新加载扩展以恢复原系统");
        return true;
    } catch (error) {
        console.error("❌ 禁用模块化系统失败:", error);
        return false;
    }
}

// 重新加载扩展
function reloadExtension() {
    console.log("🔄 重新加载扩展...");
    
    try {
        chrome.runtime.reload();
        console.log("✅ 扩展重新加载命令已发送");
    } catch (error) {
        console.error("❌ 重新加载扩展失败:", error);
        console.log("💡 请手动在扩展管理页面重新加载扩展");
    }
}

// 刷新当前页面
function refreshCurrentPage() {
    console.log("🔄 刷新当前页面...");
    
    try {
        location.reload();
        console.log("✅ 页面刷新命令已发送");
    } catch (error) {
        console.error("❌ 刷新页面失败:", error);
        console.log("💡 请手动刷新页面");
    }
}

// 显示帮助信息
function showHelp() {
    console.log("📖 神灯AI·灵阅模块化系统安全启用帮助");
    console.log("");
    console.log("🔧 可用命令:");
    console.log("  showCurrentStatus() - 显示当前模块系统状态");
    console.log("  checkModuleFiles() - 检查模块文件是否完整");
    console.log("  testModuleImport() - 测试模块导入");
    console.log("  safeEnableModularSystem() - 安全启用完整的模块化系统");
    console.log("  safeEnableFrontendModules() - 仅安全启用前端模块系统");
    console.log("  safeEnableBackendModules() - 仅启用后台模块系统");
    console.log("  disableModularSystem() - 禁用模块化系统");
    console.log("  reloadExtension() - 重新加载扩展");
    console.log("  refreshCurrentPage() - 刷新当前页面");
    console.log("  showHelp() - 显示此帮助信息");
    console.log("");
    console.log("💡 推荐使用流程:");
    console.log("  1. showCurrentStatus() - 查看当前状态");
    console.log("  2. checkModuleFiles() - 检查文件完整性");
    console.log("  3. testModuleImport() - 测试模块导入");
    console.log("  4. safeEnableModularSystem() - 安全启用模块化系统");
    console.log("  5. refreshCurrentPage() - 刷新页面");
    console.log("  6. reloadExtension() - 重启扩展");
}

// 主函数
async function main() {
    console.log("=" .repeat(60));
    console.log("🚀 神灯AI·灵阅模块化系统安全启用脚本");
    console.log("=" .repeat(60));
    
    // 检查环境
    if (!checkEnvironment()) {
        return;
    }
    
    // 显示当前状态
    const status = showCurrentStatus();
    if (!status) {
        return;
    }
    
    console.log("");
    console.log("🎯 建议操作:");
    
    if (!status.frontendEnabled && !status.backendEnabled) {
        console.log("💡 模块化系统未启用");
        console.log("📝 运行 safeEnableModularSystem() 来安全启用");
        console.log("⚠️ 启用前建议先运行 checkModuleFiles() 检查文件");
    } else if (status.frontendEnabled && status.backendEnabled) {
        console.log("✅ 模块化系统已完全启用");
        console.log("💡 运行 disableModularSystem() 来禁用");
    } else {
        console.log("⚠️ 模块化系统部分启用");
        console.log("💡 运行 safeEnableModularSystem() 来完全启用");
    }
    
    console.log("");
    console.log("📖 运行 showHelp() 查看所有可用命令");
    console.log("=" .repeat(60));
}

// 将函数添加到全局作用域
window.showCurrentStatus = showCurrentStatus;
window.checkModuleFiles = checkModuleFiles;
window.testModuleImport = testModuleImport;
window.safeEnableModularSystem = safeEnableModularSystem;
window.safeEnableFrontendModules = safeEnableFrontendModules;
window.safeEnableBackendModules = safeEnableBackendModules;
window.disableModularSystem = disableModularSystem;
window.reloadExtension = reloadExtension;
window.refreshCurrentPage = refreshCurrentPage;
window.showHelp = showHelp;

// 自动运行主函数
main();

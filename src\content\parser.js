// 神灯AI·灵阅 - 内容解析脚本
console.log("🔧 神灯AI·灵阅：内容解析脚本已加载");

// 检查API可用性
const isRuntimeAvailable = typeof chrome !== 'undefined' && typeof chrome.runtime !== 'undefined';

if (!isRuntimeAvailable) {
  console.warn("⚠️ Chrome Runtime API不可用");
}

// 解析页面内容的函数
function parsePageContent() {
  console.log("📖 开始解析页面内容");
  console.log("🌐 当前页面URL:", window.location.href);
  console.log("📄 页面标题:", document.title);

  try {
    // 检查 Readability 库是否可用
    if (typeof Readability === 'undefined') {
      console.error("❌ Readability库未加载，使用备用解析方案");
      return getFallbackContent();
    }

    console.log("✅ Readability库已加载");

    // 克隆 document 对象
    const documentClone = document.cloneNode(true);
    console.log("📋 文档已克隆");

    // 创建 Readability 实例
    const reader = new Readability(documentClone, {
      classesToPreserve: ['article', 'content', 'main', 'text', 'story']
    });

    console.log("🔧 Readability实例已创建");

    // 解析文章
    const article = reader.parse();
    console.log("🔍 Readability解析完成");

    if (article && article.textContent) {
      console.log("✅ 成功解析文章:");
      console.log("  📖 标题:", article.title);
      console.log("  📝 文本长度:", article.textContent.length);
      console.log("  👤 作者:", article.byline);

      // 添加当前页面URL到解析结果
      article.url = window.location.href;

      // 发送解析结果给背景脚本
      if (isRuntimeAvailable) {
        console.log("📤 发送解析结果到后台:", {
          title: article.title,
          url: article.url,
          contentLength: article.textContent.length
        });

        chrome.runtime.sendMessage({
          action: "parsedContent",
          article: article
        }).catch(error => {
          console.error("❌ 发送解析结果失败:", error);
        });
      }

      return article; // 返回解析结果
    } else {
      console.warn("⚠️ Readability 未能提取文章内容，使用备用方案");
      return getFallbackContent(); // 使用备用解析方案
    }
  } catch (error) {
    console.error("❌ 解析页面时出错:", error);
    return getFallbackContent(); // 异常时使用备用解析方案
  }
}

/**
 * 备用内容提取方案 - 当Readability失败时使用
 * @returns {Object} 包含标题和内容的文章对象
 */
function getFallbackContent() {
  console.log("🔄 使用备用内容提取方案");

  // 尝试常见的文章容器选择器
  const contentSelectors = [
    'article',
    '[role="main"]',
    '.content',
    '.article-content',
    '.post-content',
    '.entry-content',
    '.main-content',
    '#content',
    '#main',
    '.text',
    '.story'
  ];

  let content = '';

  for (const selector of contentSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      content = element.textContent || element.innerText || '';
      if (content.trim().length > 100) { // 确保内容足够长
        console.log(`✅ 使用选择器 "${selector}" 提取到内容，长度: ${content.length}`);
        break;
      }
    }
  }

  // 如果仍然没有找到合适的内容，使用body的文本
  if (!content || content.trim().length < 100) {
    console.log("🔄 使用body文本作为最后备用方案");
    content = document.body.textContent || document.body.innerText || '';
  }

  const article = {
    title: document.title,
    textContent: content.trim(),
    url: window.location.href,
    byline: null,
    excerpt: content.substring(0, 200) + '...'
  };

  console.log("✅ 备用方案提取完成:", {
    title: article.title,
    contentLength: article.textContent.length
  });

  // 发送解析结果给背景脚本
  if (isRuntimeAvailable) {
    chrome.runtime.sendMessage({
      action: "parsedContent",
      article: article
    }).catch(error => {
      console.error("❌ 发送备用解析结果失败:", error);
    });
  }

  return article;
}

/**
 * 查找下一页链接 - 简单版本
 */
function findNextPageLink() {
  console.log("查找下一页链接");

  // 简单的关键词列表
  const keywords = [
    '下一页', '下一章', 'next page', 'next chapter',
    '下页', '下章', '下一节', 'next section', '下节',
    '>>>', '»', '→', '▶', '▷', 'next', '下一个', '继续阅读'
  ];

  // 获取所有链接
  const links = Array.from(document.querySelectorAll('a[href]'));
  console.log(`查找下一页：共找到 ${links.length} 个链接`);

  // 遍历关键词，找到第一个匹配的链接就返回
  for (const keyword of keywords) {
    console.log(`尝试关键词: "${keyword}"`);

    for (const link of links) {
      const text = link.textContent.trim().toLowerCase();
      const href = link.href.toLowerCase();

      // 检查链接文本和href
      if (text.includes(keyword.toLowerCase()) ||
          (keyword.length > 2 && href.includes(keyword.toLowerCase()))) {

        // 排除明显不是下一页的链接
        if (text.includes('上一') || text.includes('previous') ||
            text.includes('返回') || text.includes('back') ||
            text.includes('目录') || text.includes('index')) {
          continue;
        }

        console.log(`✅ 找到下一页链接: "${link.textContent.trim()}" (关键词: ${keyword}) -> ${link.href}`);
        return link.href;
      }
    }
  }

  console.log("❌ 没有找到任何匹配的下一页链接");
  return null;
}

// 注意：内容脚本通常是一次性执行的。
// 如果需要响应来自弹出窗口或背景脚本的特定请求来重新解析，
// 则需要添加消息监听器 chrome.runtime.onMessage.addListener

// 消息监听器
if (isRuntimeAvailable) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("📨 Content Script收到消息:", message.action, "在页面:", window.location.href);

    if (message.action === 'parseContent') {
      console.log("🔄 开始解析页面内容");

      // 异步处理解析
      (async () => {
        try {
          const result = parsePageContent();
          if (result) {
            console.log("✅ 页面解析成功，发送结果到后台");
            sendResponse({
              status: "解析完成",
              success: true,
              url: window.location.href,
              title: document.title
            });
          } else {
            console.error("❌ 页面解析失败");
            sendResponse({
              status: "解析失败",
              success: false,
              error: "解析返回空结果"
            });
          }
        } catch (error) {
          console.error("❌ 页面解析出错:", error);
          sendResponse({
            status: "解析出错",
            success: false,
            error: error.message
          });
        }
      })();

      // 返回true表示异步响应
      return true;
    }

    if (message.action === 'findNextPageLink') {
      console.log("🔍 查找下一页链接");
      const nextPageUrl = findNextPageLink();
      console.log("🔗 找到的下一页链接:", nextPageUrl);
      sendResponse({
        nextPageUrl: nextPageUrl
      });
      return true;
    }

    // 其他消息类型
    return false;
  });

  console.log("✅ Content Script消息监听器已注册，页面:", window.location.href);
} else {
  console.error("❌ Chrome Runtime API不可用，无法注册消息监听器");
}

// 显示不支持API的通知
function showUnsupportedApiNotification() {
    // 创建通知元素
    const notificationDiv = document.createElement('div');
    notificationDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 9999;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        max-width: 300px;
    `;
    
    notificationDiv.innerHTML = `
        <div style="margin-bottom: 10px; font-weight: bold; color: #333;">神灯AI·灵阅</div>
        <div style="margin-bottom: 15px; color: #555;">
            您的浏览器版本不支持侧边栏功能或扩展加载出现问题。请更新到最新版本的Chrome浏览器或重新安装扩展。
        </div>
        <button id="close-notification" style="
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        ">我知道了</button>
    `;
    
    // 添加到页面
    document.body.appendChild(notificationDiv);
    
    // 添加关闭按钮事件
    document.getElementById('close-notification').addEventListener('click', () => {
        notificationDiv.remove();
    });
    
    // 自动5秒后消失
    setTimeout(() => {
        if (document.body.contains(notificationDiv)) {
            notificationDiv.remove();
        }
    }, 5000);
}

// UI控制器模块 - 管理基础UI操作和状态显示
import { BaseModule } from './BaseModule.js';

export class UIController extends BaseModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // UI元素引用
        this.elements = {};
        
        // 状态翻译映射
        this.stateTranslations = {
            'idle': '空闲',
            'reading': '朗读中',
            'paused': '已暂停',
            'stopped': '已停止',
            'loading': '加载中',
            'navigating': '切换章节中',
            'waiting_refresh': '刷新中',
            'changing_speed': '调整语速中'
        };
    }

    async onInit() {
        this.log('开始初始化UI控制器');

        // 获取核心UI元素
        this.initializeElements();

        // 设置基础事件监听器
        this.setupEventListeners();

        // 订阅状态变化
        this.subscribeToStateChanges();

        // 添加调试信息
        this.log('UI控制器初始化完成');
        this.log('播放按钮元素:', this.elements.playPauseButton ? '已找到' : '未找到');
        this.log('停止按钮元素:', this.elements.stopButton ? '已找到' : '未找到');
    }

    // 初始化DOM元素引用
    initializeElements() {
        const elementSelectors = {
            statusIndicator: '#status-indicator',
            playPauseButton: '#play-pause-btn',
            stopButton: '#stop-btn',
            speedSlider: '#speed-slider',
            speedValue: '#speed-value',
            voiceSelect: '#voice-select',
            continuousToggle: '#continuous-reading-toggle',
            chapterInput: '#continuous-read-chapters',
            messageArea: '#message-area',
            footer: '.panel-footer'
        };

        for (const [key, selector] of Object.entries(elementSelectors)) {
            this.elements[key] = this.getElement(selector, false);
            if (!this.elements[key]) {
                this.log(`警告: 未找到元素 ${selector}`, 'warn');
            }
        }

        this.log(`已初始化 ${Object.keys(this.elements).length} 个UI元素`);
    }

    // 设置事件监听器
    setupEventListeners() {
        // 播放/暂停按钮
        if (this.elements.playPauseButton) {
            this.log('设置播放按钮事件监听器');

            // 先尝试简单的事件监听器
            this.elements.playPauseButton.addEventListener('click', () => {
                console.log('🎯 播放按钮被直接点击！');
                this.handlePlayPauseClick();
            });

            this.addEventListener(
                this.elements.playPauseButton,
                'click',
                this.handlePlayPauseClick,
                'playPauseClick'
            );
        } else {
            this.log('警告: 播放按钮元素未找到，无法设置事件监听器');
        }

        // 停止按钮
        if (this.elements.stopButton) {
            this.addEventListener(
                this.elements.stopButton,
                'click',
                this.handleStopClick,
                'stopClick'
            );
        }

        // 语速滑块
        if (this.elements.speedSlider) {
            this.addEventListener(
                this.elements.speedSlider,
                'input',
                this.handleSpeedInput,
                'speedInput'
            );
            
            this.addEventListener(
                this.elements.speedSlider,
                'change',
                this.handleSpeedChange,
                'speedChange'
            );
        }

        // 语音选择
        if (this.elements.voiceSelect) {
            this.addEventListener(
                this.elements.voiceSelect,
                'change',
                this.handleVoiceChange,
                'voiceChange'
            );
        }

        // 连续阅读开关
        if (this.elements.continuousToggle) {
            this.addEventListener(
                this.elements.continuousToggle,
                'change',
                this.handleContinuousToggle,
                'continuousToggle'
            );
        }

        this.log('事件监听器设置完成');
    }

    // 订阅状态变化
    subscribeToStateChanges() {
        const stateManager = this.getDependency('stateManager');
        if (stateManager) {
            stateManager.subscribe((state, changes) => {
                this.updateUIFromState(state, changes);
            });
            this.log('已订阅状态变化');
        }
    }

    // 播放/暂停按钮点击处理
    async handlePlayPauseClick(event) {
        console.log('🎯 播放/暂停按钮被点击');
        this.log('播放/暂停按钮被点击');

        try {
            // 简化处理，直接发送开始播放命令
            console.log('📤 发送开始播放消息到后台');

            const response = await this.sendMessage({ action: 'startReading' });
            console.log('📥 收到后台响应:', response);

            this.log('发送播放命令成功');
            this.showMessage('开始播放', false);

        } catch (error) {
            console.error('❌ 播放命令失败:', error);
            this.log('播放命令失败: ' + error.message);
            this.showMessage('操作失败，请重试', true);
        }
    }

    // 停止按钮点击处理
    async handleStopClick(event) {
        this.log('停止按钮被点击');
        
        try {
            await this.sendMessage({ action: 'stopReading' });
            this.log('发送停止命令');
        } catch (error) {
            this.showMessage('停止失败，请重试', true);
        }
    }

    // 语速输入处理
    handleSpeedInput(event) {
        const speed = parseFloat(event.target.value);
        this.updateSpeedDisplay(speed);
        
        // 防抖处理
        this.debounceSpeedChange(speed);
    }

    // 语速变化处理
    async handleSpeedChange(event) {
        const speed = parseFloat(event.target.value);
        const safeSpeed = Math.max(0.5, Math.min(5.0, speed));
        
        try {
            await this.sendMessage({ action: 'setSpeed', speed: safeSpeed });
            this.log(`语速设置为: ${safeSpeed}`);
        } catch (error) {
            this.showMessage('语速调整失败', true);
        }
    }

    // 语音选择变化处理
    async handleVoiceChange(event) {
        const voiceName = event.target.value;
        
        try {
            await this.sendMessage({ action: 'setVoice', payload: voiceName });
            this.log(`语音设置为: ${voiceName}`);
        } catch (error) {
            this.showMessage('语音设置失败', true);
        }
    }

    // 连续阅读开关处理
    async handleContinuousToggle(event) {
        const enabled = event.target.checked;
        
        try {
            await this.sendMessage({ action: 'setContinuousReading', continuous: enabled });
            this.log(`连续阅读设置为: ${enabled}`);
        } catch (error) {
            this.showMessage('连续阅读设置失败', true);
        }
    }

    // 防抖语速变化
    debounceSpeedChange(speed) {
        const resourceManager = this.getDependency('resourceManager');
        
        if (this.speedChangeTimeout) {
            resourceManager.clearTimeout('speedChange');
        }
        
        this.speedChangeTimeout = resourceManager.createTimeout(
            'speedChange',
            async () => {
                const stateManager = this.getDependency('stateManager');
                const currentState = stateManager.getState();
                
                if (currentState.readingState === 'reading') {
                    try {
                        await this.sendMessage({ action: 'setSpeed', speed });
                        this.log(`实时语速调整: ${speed}`);
                    } catch (error) {
                        this.log('实时语速调整失败', 'warn');
                    }
                }
            },
            300
        );
    }

    // 更新语速显示
    updateSpeedDisplay(speed) {
        if (this.elements.speedValue) {
            this.elements.speedValue.textContent = `${speed.toFixed(1)}x`;
        }
    }

    // 根据状态更新UI
    updateUIFromState(state, changes) {
        this.log('根据状态更新UI', 'debug');
        
        // 更新状态指示器
        this.updateStatusIndicator(state);
        
        // 更新播放/暂停按钮
        this.updatePlayPauseButton(state);
        
        // 更新停止按钮
        this.updateStopButton(state);
        
        // 更新控件启用状态
        this.updateControlsState(state);
        
        // 更新语速显示
        if (changes.includes('currentSpeed')) {
            this.updateSpeedControls(state.currentSpeed);
        }
        
        // 更新语音选择
        if (changes.includes('currentVoice')) {
            this.updateVoiceSelect(state.currentVoice);
        }
        
        // 更新连续阅读开关
        if (changes.includes('continuousReading')) {
            this.updateContinuousToggle(state.continuousReading);
        }
    }

    // 更新状态指示器
    updateStatusIndicator(state) {
        if (!this.elements.statusIndicator) return;
        
        const statusText = this.translateState(state.readingState);
        this.elements.statusIndicator.textContent = statusText;
        this.elements.statusIndicator.className = `status-indicator ${state.readingState || 'idle'}`;
    }

    // 更新播放/暂停按钮
    updatePlayPauseButton(state) {
        if (!this.elements.playPauseButton) return;

        const isPlaying = state.readingState === 'reading' || state.readingState === 'navigating';

        if (isPlaying) {
            this.elements.playPauseButton.classList.add('playing');
            this.elements.playPauseButton.title = '暂停播放';
        } else {
            this.elements.playPauseButton.classList.remove('playing');
            // 根据不同状态显示不同的提示文本
            if (state.readingState === 'paused') {
                this.elements.playPauseButton.title = '继续播放';
            } else if (state.readingState === 'stopped') {
                this.elements.playPauseButton.title = '重新开始播放';
            } else {
                this.elements.playPauseButton.title = '开始播放';
            }
        }

        this.elements.playPauseButton.disabled = state.readingState === 'loading';
    }

    // 更新停止按钮
    updateStopButton(state) {
        if (!this.elements.stopButton) return;

        const canStop = ['reading', 'paused', 'navigating'].includes(state.readingState);
        this.elements.stopButton.disabled = !canStop;

        // 在stopped状态下，停止按钮应该被禁用，因为已经停止了
        if (state.readingState === 'stopped') {
            this.elements.stopButton.disabled = true;
        }
    }

    // 更新控件状态
    updateControlsState(state) {
        const isPlaying = ['reading', 'paused', 'navigating'].includes(state.readingState);

        // 禁用/启用设置控件
        // 在播放、暂停、导航状态下禁用设置控件
        // 在idle、stopped、loading状态下启用设置控件
        if (this.elements.continuousToggle) {
            this.elements.continuousToggle.disabled = isPlaying;
        }

        if (this.elements.chapterInput) {
            this.elements.chapterInput.disabled = isPlaying;
        }
    }

    // 更新语速控件
    updateSpeedControls(speed) {
        if (this.elements.speedSlider && parseFloat(this.elements.speedSlider.value) !== speed) {
            this.elements.speedSlider.value = speed;
            this.updateSpeedDisplay(speed);
        }
    }

    // 更新语音选择
    updateVoiceSelect(voiceName) {
        if (this.elements.voiceSelect && this.elements.voiceSelect.value !== voiceName) {
            this.elements.voiceSelect.value = voiceName || '';
        }
    }

    // 更新连续阅读开关
    updateContinuousToggle(enabled) {
        if (this.elements.continuousToggle && this.elements.continuousToggle.checked !== enabled) {
            this.elements.continuousToggle.checked = enabled;
        }
    }

    // 翻译状态
    translateState(state) {
        return this.stateTranslations[state] || state;
    }

    // 显示消息
    showMessage(message, isError = false) {
        if (!this.elements.messageArea || !this.elements.footer) return;
        
        this.elements.messageArea.textContent = message;
        this.elements.messageArea.classList.toggle('error-message', isError);
        this.elements.footer.style.display = 'block';
        
        // 自动隐藏消息
        const resourceManager = this.getDependency('resourceManager');
        resourceManager.createTimeout('hideMessage', () => {
            this.hideMessage();
        }, 3000);
    }

    // 隐藏消息
    hideMessage() {
        if (!this.elements.messageArea || !this.elements.footer) return;
        
        this.elements.messageArea.textContent = '';
        this.elements.messageArea.classList.remove('error-message');
        this.elements.footer.style.display = 'none';
    }

    // 获取UI状态
    getUIState() {
        return {
            elements: Object.keys(this.elements).reduce((acc, key) => {
                acc[key] = !!this.elements[key];
                return acc;
            }, {}),
            eventListeners: this.eventListeners.size
        };
    }

    async onDestroy() {
        // 清理防抖定时器
        const resourceManager = this.getDependency('resourceManager');
        resourceManager.clearTimeout('speedChange');
        resourceManager.clearTimeout('hideMessage');
        
        this.log('UI控制器已清理');
    }
}

export { UIController };

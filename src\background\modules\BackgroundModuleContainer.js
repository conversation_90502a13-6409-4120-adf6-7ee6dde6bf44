// 后台模块容器 - 管理所有后台模块的生命周期
export class BackgroundModuleContainer {
    constructor() {
        this.modules = new Map();
        this.moduleClasses = new Map();
        this.dependencies = {};
        this.isInitialized = false;
        this.eventListeners = new Map();
        
        console.log('📦 后台模块容器已创建');
    }

    // 注册模块类
    registerModule(name, moduleClass, config = {}) {
        if (this.moduleClasses.has(name)) {
            console.warn(`⚠️ 模块 ${name} 已经注册过了，将被覆盖`);
        }

        this.moduleClasses.set(name, {
            class: moduleClass,
            config: config,
            registeredAt: Date.now()
        });

        console.log(`📝 后台模块类已注册: ${name}`);
        return this;
    }

    // 设置依赖
    setDependencies(dependencies) {
        this.dependencies = { ...this.dependencies, ...dependencies };
        console.log('🔗 后台模块依赖已设置:', Object.keys(dependencies));
        return this;
    }

    // 初始化所有模块
    async initializeAll() {
        if (this.isInitialized) {
            console.warn('⚠️ 后台模块容器已经初始化过了');
            return [];
        }

        console.log('🚀 开始初始化所有后台模块...');
        
        const results = [];
        const startTime = performance.now();

        // 按依赖顺序初始化模块
        const initOrder = this.calculateInitOrder();
        
        for (const moduleName of initOrder) {
            try {
                const result = await this.initializeModule(moduleName);
                results.push(result);
            } catch (error) {
                console.error(`❌ 模块 ${moduleName} 初始化失败:`, error);
                results.push({
                    name: moduleName,
                    success: false,
                    error: error.message,
                    duration: 0
                });
            }
        }

        const totalDuration = performance.now() - startTime;
        const successCount = results.filter(r => r.success).length;
        
        console.log(`✅ 后台模块初始化完成: ${successCount}/${results.length} 成功，耗时: ${totalDuration.toFixed(2)}ms`);
        
        this.isInitialized = true;
        return results;
    }

    // 计算初始化顺序
    calculateInitOrder() {
        // 定义模块依赖关系
        const dependencies = {
            'StateManager': [],
            'TTSManager': ['StateManager'],
            'ContentManager': ['StateManager'],
            'MessageHandler': ['StateManager', 'TTSManager', 'ContentManager'],
            'StorageManager': []
        };

        const visited = new Set();
        const visiting = new Set();
        const result = [];

        const visit = (moduleName) => {
            if (visiting.has(moduleName)) {
                throw new Error(`检测到循环依赖: ${moduleName}`);
            }
            
            if (visited.has(moduleName)) {
                return;
            }

            visiting.add(moduleName);
            
            const deps = dependencies[moduleName] || [];
            deps.forEach(dep => {
                if (this.moduleClasses.has(dep)) {
                    visit(dep);
                }
            });

            visiting.delete(moduleName);
            visited.add(moduleName);
            result.push(moduleName);
        };

        // 访问所有注册的模块
        for (const moduleName of this.moduleClasses.keys()) {
            visit(moduleName);
        }

        return result;
    }

    // 初始化单个模块
    async initializeModule(name) {
        const moduleInfo = this.moduleClasses.get(name);
        if (!moduleInfo) {
            throw new Error(`模块 ${name} 未注册`);
        }

        const startTime = performance.now();
        
        try {
            // 创建模块实例
            const moduleInstance = new moduleInfo.class(name, {
                ...this.dependencies,
                moduleContainer: this
            });

            // 初始化模块
            await moduleInstance.init();

            // 保存模块实例
            this.modules.set(name, moduleInstance);

            const duration = performance.now() - startTime;
            console.log(`✅ 后台模块 ${name} 初始化成功，耗时: ${duration.toFixed(2)}ms`);

            return {
                name: name,
                success: true,
                duration: duration,
                status: moduleInstance.getStatus()
            };

        } catch (error) {
            const duration = performance.now() - startTime;
            console.error(`❌ 后台模块 ${name} 初始化失败:`, error);
            
            return {
                name: name,
                success: false,
                error: error.message,
                duration: duration
            };
        }
    }

    // 获取模块
    getModule(name) {
        return this.modules.get(name);
    }

    // 获取所有模块
    getAllModules() {
        return Array.from(this.modules.values());
    }

    // 获取模块名称列表
    getModuleNames() {
        return Array.from(this.modules.keys());
    }

    // 检查模块是否存在
    hasModule(name) {
        return this.modules.has(name);
    }

    // 销毁所有模块
    async destroyAll() {
        console.log('🗑️ 开始销毁所有后台模块...');
        
        const results = [];
        
        // 按相反顺序销毁模块
        const destroyOrder = Array.from(this.modules.keys()).reverse();
        
        for (const moduleName of destroyOrder) {
            try {
                const module = this.modules.get(moduleName);
                if (module) {
                    await module.destroy();
                    this.modules.delete(moduleName);
                    results.push({ name: moduleName, success: true });
                }
            } catch (error) {
                console.error(`❌ 销毁模块 ${moduleName} 失败:`, error);
                results.push({ name: moduleName, success: false, error: error.message });
            }
        }

        // 清理事件监听器
        this.eventListeners.clear();
        
        this.isInitialized = false;
        console.log('✅ 所有后台模块已销毁');
        
        return results;
    }

    // 销毁单个模块
    async destroyModule(name) {
        const module = this.modules.get(name);
        if (!module) {
            throw new Error(`模块 ${name} 不存在`);
        }

        try {
            await module.destroy();
            this.modules.delete(name);
            console.log(`✅ 后台模块 ${name} 已销毁`);
            return true;
        } catch (error) {
            console.error(`❌ 销毁模块 ${name} 失败:`, error);
            throw error;
        }
    }

    // 重启模块
    async restartModule(name) {
        console.log(`🔄 重启后台模块: ${name}`);
        
        // 先销毁
        await this.destroyModule(name);
        
        // 再初始化
        const result = await this.initializeModule(name);
        
        if (result.success) {
            console.log(`✅ 后台模块 ${name} 重启成功`);
        } else {
            console.error(`❌ 后台模块 ${name} 重启失败:`, result.error);
        }
        
        return result;
    }

    // 启用模块
    enableModule(name) {
        const module = this.modules.get(name);
        if (module && typeof module.enable === 'function') {
            module.enable();
            console.log(`✅ 后台模块 ${name} 已启用`);
            return true;
        }
        return false;
    }

    // 禁用模块
    disableModule(name) {
        const module = this.modules.get(name);
        if (module && typeof module.disable === 'function') {
            module.disable();
            console.log(`❌ 后台模块 ${name} 已禁用`);
            return true;
        }
        return false;
    }

    // 获取容器状态
    getStatus() {
        const moduleStatuses = {};
        
        for (const [name, module] of this.modules) {
            moduleStatuses[name] = module.getStatus();
        }

        return {
            isInitialized: this.isInitialized,
            moduleCount: this.modules.size,
            registeredClasses: this.moduleClasses.size,
            modules: moduleStatuses,
            dependencies: Object.keys(this.dependencies),
            eventListeners: this.eventListeners.size
        };
    }

    // 事件系统
    on(eventType, listener) {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, new Set());
        }
        
        this.eventListeners.get(eventType).add(listener);
        
        return () => {
            const listeners = this.eventListeners.get(eventType);
            if (listeners) {
                listeners.delete(listener);
            }
        };
    }

    // 触发事件
    emit(eventType, data) {
        const listeners = this.eventListeners.get(eventType);
        if (listeners) {
            listeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    console.error(`❌ 事件监听器错误 (${eventType}):`, error);
                }
            });
        }
    }

    // 移除事件监听器
    off(eventType, listener) {
        const listeners = this.eventListeners.get(eventType);
        if (listeners) {
            listeners.delete(listener);
        }
    }

    // 健康检查
    async healthCheck() {
        const results = {};
        
        for (const [name, module] of this.modules) {
            try {
                if (typeof module.healthCheck === 'function') {
                    results[name] = await module.healthCheck();
                } else {
                    results[name] = {
                        status: 'ok',
                        available: module.isAvailable(),
                        enabled: module.isEnabled
                    };
                }
            } catch (error) {
                results[name] = {
                    status: 'error',
                    error: error.message
                };
            }
        }
        
        return results;
    }

    // 获取性能报告
    getPerformanceReport() {
        const report = {
            container: {
                moduleCount: this.modules.size,
                isInitialized: this.isInitialized,
                eventListeners: this.eventListeners.size
            },
            modules: {}
        };

        for (const [name, module] of this.modules) {
            const status = module.getStatus();
            report.modules[name] = {
                isEnabled: status.isEnabled,
                isInitialized: status.isInitialized,
                messageListeners: status.messageListeners,
                eventListeners: status.eventListeners,
                timers: status.timers
            };
        }

        return report;
    }
}

// 自动注册所有后台模块的函数
export async function registerAllBackgroundModules(container) {
    try {
        // 导入所有后台模块
        const { TTSManager } = await import('./TTSManager.js');
        const { ContentManager } = await import('./ContentManager.js');
        const { MessageHandler } = await import('./MessageHandler.js');
        const { StateManager } = await import('./StateManager.js');
        
        // 注册所有模块
        container.registerModule('StateManager', StateManager);
        container.registerModule('TTSManager', TTSManager);
        container.registerModule('ContentManager', ContentManager);
        container.registerModule('MessageHandler', MessageHandler);
        
        console.log('✅ 所有后台模块已注册');
        return true;
    } catch (error) {
        console.error('❌ 后台模块注册失败:', error);
        return false;
    }
}

// 创建全局后台模块容器实例
export const backgroundModuleContainer = new BackgroundModuleContainer();

// 便捷函数
export const registerBackgroundModule = (name, moduleClass, config) => 
    backgroundModuleContainer.registerModule(name, moduleClass, config);

export const getBackgroundModule = (name) => 
    backgroundModuleContainer.getModule(name);

export const initializeBackgroundModules = () => 
    backgroundModuleContainer.initializeAll();

export const destroyBackgroundModules = () => 
    backgroundModuleContainer.destroyAll();

console.log('📦 后台模块容器系统已加载');

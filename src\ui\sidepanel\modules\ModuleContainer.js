// 模块容器 - 管理所有UI模块的依赖注入和生命周期
import { resourceManager } from '../../../utils/resourceManager.js';
import { stateManager } from '../../../utils/stateManager.js';
import { configManager } from '../../../utils/configManager.js';
import { performanceMonitor } from '../../../utils/performanceMonitor.js';
import { errorHandler } from '../../../utils/errorHandler.js';

export class ModuleContainer {
    constructor() {
        this.modules = new Map();
        this.dependencies = this.createDependencies();
        this.isInitialized = false;
        
        console.log('📦 ModuleContainer 已创建');
    }

    // 创建依赖容器
    createDependencies() {
        return {
            resourceManager,
            stateManager,
            configManager,
            performanceMonitor,
            errorHandler,
            
            // DOM相关依赖
            document: document,
            window: window,
            chrome: chrome,
            
            // 模块容器自身
            moduleContainer: this
        };
    }

    // 注册模块
    registerModule(name, moduleClass, config = {}) {
        try {
            if (this.modules.has(name)) {
                console.warn(`⚠️ 模块 ${name} 已存在，将被替换`);
            }

            // 创建模块实例
            const moduleInstance = new moduleClass(name, this.dependencies);
            
            // 保存模块信息
            this.modules.set(name, {
                instance: moduleInstance,
                class: moduleClass,
                config: config,
                registeredAt: Date.now()
            });

            console.log(`✅ 模块 ${name} 已注册`);
            return moduleInstance;

        } catch (error) {
            errorHandler.handleError(error, 'module_registration', {
                moduleName: name,
                moduleClass: moduleClass.name
            });
            throw error;
        }
    }

    // 获取模块
    getModule(name) {
        const moduleInfo = this.modules.get(name);
        return moduleInfo ? moduleInfo.instance : null;
    }

    // 检查模块是否存在
    hasModule(name) {
        return this.modules.has(name);
    }

    // 初始化所有模块
    async initializeAll() {
        if (this.isInitialized) {
            console.warn('⚠️ ModuleContainer 已经初始化过了');
            return;
        }

        console.log('🚀 开始初始化所有模块');
        
        const startTime = performance.now();
        const results = [];

        for (const [name, moduleInfo] of this.modules) {
            try {
                console.log(`🔄 初始化模块: ${name}`);
                await moduleInfo.instance.init();
                results.push({ name, success: true });
            } catch (error) {
                console.error(`❌ 模块 ${name} 初始化失败:`, error);
                results.push({ name, success: false, error });
            }
        }

        const endTime = performance.now();
        const duration = endTime - startTime;

        this.isInitialized = true;

        console.log(`✅ 模块初始化完成，耗时: ${duration.toFixed(2)}ms`);
        console.log('📊 初始化结果:', results);

        return results;
    }

    // 销毁所有模块
    async destroyAll() {
        if (!this.isInitialized) {
            return;
        }

        console.log('🗑️ 开始销毁所有模块');

        for (const [name, moduleInfo] of this.modules) {
            try {
                await moduleInfo.instance.destroy();
            } catch (error) {
                console.error(`❌ 模块 ${name} 销毁失败:`, error);
            }
        }

        this.isInitialized = false;
        console.log('✅ 所有模块已销毁');
    }

    // 启用模块
    enableModule(name) {
        const module = this.getModule(name);
        if (module) {
            module.enable();
            return true;
        }
        return false;
    }

    // 禁用模块
    disableModule(name) {
        const module = this.getModule(name);
        if (module) {
            module.disable();
            return true;
        }
        return false;
    }

    // 获取所有模块状态
    getModuleStatuses() {
        const statuses = {};
        for (const [name, moduleInfo] of this.modules) {
            statuses[name] = moduleInfo.instance.getStatus();
        }
        return statuses;
    }

    // 检查是否使用新模块系统
    shouldUseNewModules() {
        return configManager.getConfig('debug.useNewModules', false);
    }

    // 启用新模块系统
    enableNewModules() {
        configManager.setConfig('debug.useNewModules', true);
        console.log('✅ 新模块系统已启用');
    }

    // 禁用新模块系统
    disableNewModules() {
        configManager.setConfig('debug.useNewModules', false);
        console.log('❌ 新模块系统已禁用');
    }

    // 获取容器状态
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            moduleCount: this.modules.size,
            useNewModules: this.shouldUseNewModules(),
            modules: this.getModuleStatuses()
        };
    }
}

// 自动注册所有模块的函数
export async function registerAllModules(container) {
    try {
        // 导入所有模块
        const { UIController } = await import('./UIController.js');
        const { VoiceManager } = await import('./VoiceManager.js');
        const { PlaybackController } = await import('./PlaybackController.js');
        const { URLHandler } = await import('./URLHandler.js');
        const { SettingsManager } = await import('./SettingsManager.js');
        const { EventManager } = await import('./EventManager.js');

        // 注册所有模块
        container.registerModule('UIController', UIController);
        container.registerModule('VoiceManager', VoiceManager);
        container.registerModule('PlaybackController', PlaybackController);
        container.registerModule('URLHandler', URLHandler);
        container.registerModule('SettingsManager', SettingsManager);
        container.registerModule('EventManager', EventManager);

        console.log('✅ 所有模块已注册');
        return true;
    } catch (error) {
        console.error('❌ 模块注册失败:', error);
        return false;
    }
}

// 创建全局模块容器实例
export const moduleContainer = new ModuleContainer();

// 便捷函数
export const registerModule = (name, moduleClass, config) =>
    moduleContainer.registerModule(name, moduleClass, config);

export const getModule = (name) =>
    moduleContainer.getModule(name);

export const initializeModules = () =>
    moduleContainer.initializeAll();

export const destroyModules = () =>
    moduleContainer.destroyAll();

console.log('📦 ModuleContainer 系统已加载');
